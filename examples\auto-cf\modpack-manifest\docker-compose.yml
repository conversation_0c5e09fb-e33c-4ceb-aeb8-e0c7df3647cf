services:
  mc:
    image: itzg/minecraft-server
    environment:
      EULA: true
      MODPACK_PLATFORM: AUTO_CURSEFORGE
      # Allocate API key from https://console.curseforge.com/
      # and set in .env file making sure to double up dollar signs, such as
      # CF_API_KEY=$$2a$$10$$....
      # Refer to https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#api-key
      CF_API_KEY: ${CF_API_KEY}
      CF_MODPACK_MANIFEST: /manifests/manifest.json
      CF_SLUG: "custom"
      MEMORY: 2G
    volumes:
      - mc-data:/data
      - ./manifests:/manifests:ro
    ports:
      - "25565:25565"

volumes:
  mc-data: {}
