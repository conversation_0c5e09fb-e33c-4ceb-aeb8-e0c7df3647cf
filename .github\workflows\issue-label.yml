name: Issue labels

on:
  issues:
    types: [labeled, reopened, closed, deleted]

env:
  DISCORD_WEBHOOK: ${{ secrets.DISCORD_ISSUES_WEBHOOK }}

jobs:
  labelNotify:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: notify on label
        if: > 
          github.event.label.name == 'enhancement' 
          || github.event.label.name == 'bug' 
          || github.event.label.name == 'help wanted'
          || github.event.label.name == 'priority/high'
          || github.event.label.name == 'question'
          || github.event.label.name == 'status/waiting on upstream'
        uses: Ilshidur/action-discord@master
        with:
          args: "[${{ github.event.issue.title }} (#${{ github.event.issue.number }})](<${{ github.event.issue.html_url }}>) added `${{ github.event.label.name }}` label"
      - name: notify on action change
        if: > 
          github.event.action == 'closed'
          || github.event.action == 'reopened'
          || github.event.action == 'deleted'
        uses: Ilshidur/action-discord@master
        with:
          args: "[${{ github.event.issue.title }} (#${{ github.event.issue.number }})](<${{ github.event.issue.html_url }}>) has been `${{ github.event.action }}` by `${{ github.event.sender.login }}`"
