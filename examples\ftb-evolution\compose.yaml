services:
  mc:
    image: itzg/minecraft-server
    tty: true
    stdin_open: true
    ports:
      # Game port
      - "25565:25565/tcp"
    environment:
      EULA: "TRUE"
      TYPE: AUTO_CURSEFORGE
      # Allocate API key from https://console.curseforge.com/
      # and set in .env file making sure to double up dollar signs, such as
      # CF_API_KEY=$$2a$$10$$....
      # Refer to https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#api-key
      CF_API_KEY: ${CF_API_KEY}
      CF_PAGE_URL: https://www.curseforge.com/minecraft/modpacks/ftb-evolution
      # This modpack includes an override client-side only mod that will prevent the server from starting.
      # For more information on override exclusions see: 
      # https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#excluding-overrides-files
      CF_OVERRIDES_EXCLUSIONS: mods/rgp_client*.jar
      MEMORY: 6G
    volumes:
      # attach the relative directory 'data' to the container's /data path
      - ./data:/data