#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"
isDebugging && set -x

set -eo pipefail

spigotBuildLog="/data/spigot_build.log"

function handleFailedSpigotBuild {
  logError "Failed to build Spigot"
  cat ${spigotBuildLog}
  exit 1
}


function buildSpigotFromSource {
  local tempDir="/data/temp"

  if [[ ${TYPE^^} = *BUKKIT ]] && ! versionLessThan "1.14"; then
    logError "Craftbukkit build is only supported for versions less than 1.14"
    exit 1
  fi

  log "Building Spigot $VANILLA_VERSION from source, might take a while, get some coffee"
  rm -rf ${tempDir}
  mkdir ${tempDir}
  cd ${tempDir}

  jvmOpts="-Xms${INIT_MEMORY:-$MEMORY} -Xmx${MAX_MEMORY:-$MEMORY}"

  logn ''
  curl -sSL -o ${tempDir}/BuildTools.jar https://hub.spigotmc.org/jenkins/job/BuildTools/lastSuccessfulBuild/artifact/target/BuildTools.jar && \
    java $jvmOpts -jar ${tempDir}/BuildTools.jar --rev "$VERSION" 2>&1 |tee ${spigotBuildLog}| while read l; do echo -n .; done; log "done"

  case ${TYPE^^} in
    SPIGOT)
      if ! mv spigot-*.jar "/data/${SERVER}"; then
        handleFailedSpigotBuild
      fi
      ;;
    *BUKKIT)
      if ! mv craftbukkit-*.jar "/data/${SERVER}"; then
        handleFailedSpigotBuild
      fi
      ;;
  esac

  log "Cleaning up"
  rm -rf ${tempDir}
  cd /data
}

function downloadSpigot {
  local match
  local getBukkitBaseUrl="https://getbukkit.org/download/"
  local getBukkitSpigotUrl="${getBukkitBaseUrl}spigot" 
  case "$TYPE" in
    *BUKKIT|*bukkit)
      match="CraftBukkit"
      downloadUrl=${BUKKIT_DOWNLOAD_URL}
      getbukkitFlavor=craftbukkit
      ;;
    *)
      match="Spigot"
      downloadUrl=${SPIGOT_DOWNLOAD_URL}
      getbukkitFlavor=spigot
      ;;
  esac

  if [[ ${VERSION^^} = LATEST ]]; then
    if ! VERSION=$(restify ${getBukkitSpigotUrl} --attribute='property=og:title' | jq -r '.[0] | .attributes | select(.property == "og:title") | .content | split(" ") | .[-1]'); then
      logError "Failed to retrieve latest version from ${getBukkitSpigotUrl} -- site might be down"
      exit 1
    fi
  fi

  if [[ -z $downloadUrl ]]; then
    downloadBaseUrl="https://"
    downloadSuffixUrl=".getbukkit.org/${getbukkitFlavor}/${getbukkitFlavor}-${VERSION}.jar"
    if versionLessThan 1.16.5 || { [[ ${getbukkitFlavor} = "craftbukkit" ]] && [[ ${VERSION} = "1.16.5" ]] ; }; then
      downloadBaseUrl+="cdn"
    else
      downloadBaseUrl+="download"
    fi
    downloadUrl="${downloadBaseUrl}${downloadSuffixUrl}"
  fi

  setServerVar
  curlArgs=()
  if [ -f "$SERVER" ] && ! isTrue "$FORCE_REDOWNLOAD"; then
    # tell curl to only download when newer
    curlArgs+=(-z "$SERVER")
  fi
  if isDebugging; then
    curlArgs+=(-v)
  fi
  log "Downloading $match from $downloadUrl ..."

  tempFile="$SERVER.$$"

  # HTTP error or download site responded with an HTML error page
  if ! curl -fsSL -o "$tempFile" "${curlArgs[@]}" "$downloadUrl" || ( [ -f "$tempFile" ] && grep -iq "doctype html" "$tempFile" ); then

    cat <<EOF

ERROR: failed to download from $downloadUrl
       Visit ${getBukkitBaseUrl}${getbukkitFlavor} to lookup the
       exact version or see if download site is unavailable.
       Click into the version entry to find the **exact** version.

EOF

    if isDebugging && grep -iq "doctype html" "$tempFile"; then
      cat "$tempFile"
    fi

    if [ -f "$SERVER" ]; then
      log "Continuing with existing $SERVER file"
    else
      # remove invalid download
      rm "$tempFile"
      exit 3
    fi

  else
    if [ -f "$tempFile" ]; then
      mv "$tempFile" "$SERVER"
    fi

  fi


  JVM_OPTS="${JVM_OPTS} -DIReallyKnowWhatIAmDoingISwear"
  export JVM_OPTS
}

function setServerVar {
  case "$TYPE" in
    *BUKKIT|*bukkit)
      export SERVER=craftbukkit_server-${VERSION}.jar
      ;;
    *)
      export SERVER=spigot_server-${VERSION}.jar
      ;;
  esac
}

if isTrue "$BUILD_SPIGOT_FROM_SOURCE" || isTrue "$BUILD_FROM_SOURCE"; then
  resolveVersion
  setServerVar
  if [ ! -f "$SERVER" ] || isTrue "$FORCE_REDOWNLOAD"; then
    buildSpigotFromSource
  fi
else
  downloadSpigot
fi

# Normalize on Spigot for operations below
export FAMILY=SPIGOT

exec "${SCRIPTS:-/}start-spiget" "$@"
