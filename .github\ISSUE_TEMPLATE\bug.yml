name: Bug Report
description: File a bug report
labels:
  - bug
  - status/needs triage
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug.
        
        Please double check some things first:
        1. Do you just have a question about something? If so, asking in the [Q&A Discussions](https://github.com/itzg/docker-minecraft-server/discussions/categories/q-a) or asking on [the Discord server](https://discord.gg/DXfKpjB) would be best.
        2. Did you **re-pull the newest image** and confirmed the issue after that? Run `docker pull <image>` or if using a compose file, it's as easy as running `docker compose pull`. If using Kubernetes, add `imagePullPolicy: Always` to the container. 
        4. Is this bug happening after the `[init]` prefixed logs and after the log that says "Starting the Minecraft server"? If so, please report the bug with Mojang or the respective server provider.
        5. Are you seeing a performance problem? If so, that is typically outside the scope of this image. Ask a question as above or contact the respective server provider.
  - type: textarea
    id: problem
    attributes:
      label: Describe the problem
    validations:
      required: true
  - type: textarea
    id: container
    attributes:
      label: Container definition
      description: Please provide the compose file or run command used to create the container
      value: |
        ```
        Paste run command or compose file here
        ```
  - type: textarea
    id: logs
    attributes:
      label: Container logs
      description: |
        Please provide container logs from the start of the container, which will be the ones prefixed with `[init]`. It is even better if you can set the variable `DEBUG` to "true" and provide those debug container logs.
      value: |
        ```
        Paste logs here
        ```
