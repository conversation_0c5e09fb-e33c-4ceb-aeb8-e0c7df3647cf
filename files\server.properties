op-permission-level=4
allow-nether=true
level-name=world
enable-query=false
allow-flight=false
announce-player-achievements=true
server-port=25565
rcon.port=25575
query.port=25565
level-type=DEFAULT
enable-rcon=false
force-gamemode=false
level-seed=
server-ip=
max-tick-time=60000
max-build-height=256
spawn-npcs=true
white-list=false
spawn-animals=true
hardcore=false
snooper-enabled=true
texture-pack=
online-mode=true
resource-pack=
resource-pack-sha1=
pvp=true
difficulty=1
enable-command-block=true
player-idle-timeout=0
gamemode=0
max-players=20
spawn-monsters=true
generate-structures=true
view-distance=10
spawn-protection=16
motd=A Minecraft Server powered by Docker
generator-settings=
rcon.password=
max-world-size=29999984
broadcast-console-to-ops=true
broadcast-rcon-to-ops=true
enable-jmx-monitoring=false
sync-chunk-writes=true
enable-status=true
entity-broadcast-range-percentage=100
function-permission-level=2
network-compression-threshold=256
op-permission-level=4
prevent-proxy-connections=false
use-native-transport=true
enforce-whitelist=false
