#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"
set -o pipefail
handleDebugMode

: "${PAPER_PROJECT:=paper}"
: "${PAPER_BUILD:=${PAPERBUILD:-}}"
: "${PAPER_CHANNEL:=default}"
: "${PAPER_DOWNLOAD_URL:=}"
: "${PAPER_CUSTOM_JAR:=}"
: "${PAPER_CONFIG_DEFAULTS_REPO:=${PAPER_CONFIG_REPO:=https://raw.githubusercontent.com/dayyeeet/minecraft-default-configs/main}}"

resultsFile=/data/.paper.env
if [[ $PAPER_CUSTOM_JAR ]]; then
  export SERVER="$PAPER_CUSTOM_JAR"
elif [[ $PAPER_DOWNLOAD_URL ]]; then
  if ! mc-image-helper install-paper \
    --output-directory=/data \
    --results-file="$resultsFile" \
    --url="$PAPER_DOWNLOAD_URL"; then
      logError "Failed to download from custom PaperMC URL"
      exit 1
  fi
  applyResultsFile ${resultsFile}
else
  args=(
    --output-directory=/data
    --results-file="$resultsFile"
    --project="$PAPER_PROJECT"
    --version="$VERSION"
    --channel="$PAPER_CHANNEL"
  )
  if [[ $PAPER_BUILD ]]; then
    args+=(--build="$PAPER_BUILD")
  fi
  if ! mc-image-helper install-paper "${args[@]}"; then
      logError "Failed to download $PAPER_PROJECT"
      exit 1
  fi
  applyResultsFile ${resultsFile}
fi

defaultTopLevelConfigs="bukkit.yml spigot.yml"
if versionLessThan 1.19; then
  defaultTopLevelConfigs+=" paper.yml"
else
  # Download default configs to allow for consistent patching
  DOWNLOAD_DEFAULT_CONFIGS=$(buildDownloadList "$PAPER_CONFIG_DEFAULTS_REPO" "$VERSION" paper-global.yml paper-world-defaults.yml)
  export DOWNLOAD_DEFAULT_CONFIGS
fi

# Download top-level configs to allow for consistent patching
DOWNLOAD_DEFAULTS=$(buildDownloadList "$PAPER_CONFIG_DEFAULTS_REPO" "$VERSION" $defaultTopLevelConfigs)
export DOWNLOAD_DEFAULTS

# Normalize on Spigot for downstream operations
export FAMILY=SPIGOT

exec "${SCRIPTS:-/}start-spiget" "$@"
