#!/bin/bash
. "/start-utils"
: "${CONSOLE_IN_NAMED_PIPE:=/tmp/minecraft-console-in}"

if isFalse "${CREATE_CONSOLE_IN_PIPE:-false}"; then
  error "Console pipe needs to be enabled by setting CREATE_CONSOLE_IN_PIPE to true"
fi


if [ $# = 0 ]; then
  error "Pass console commands as arguments"
  exit 1
fi

if [ ! -p "${CONSOLE_IN_NAMED_PIPE}" ]; then
  error "Named pipe ${CONSOLE_IN_NAMED_PIPE} is missing"
  exit 1
fi

if [[ "$(id -u)" = 0 ]] && [[ $UID != 0 ]]; then
  error "Exec needs to be run with user ID $UID"
  exit 2
else
  echo "$@" >"${CONSOLE_IN_NAMED_PIPE}"
fi
