---
site_name: Minecraft Server on Docker (Java Edition)
site_url: https://docker-minecraft-server.readthedocs.io/en/latest/
site_description: Documentation for Minecraft Server on Docker
repo_url: https://github.com/itzg/docker-minecraft-server
edit_uri: blob/master/docs/
theme:
  name: material
  features:
    - navigation.tracking
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.sections
    - navigation.expand
    - navigation.top
    - navigation.indexes
  locale: en
  palette:

    # Palette toggle for automatic mode
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode

    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/brightness-4
        name: Switch to system preference

  highlightjs: true
  hljs_languages:
    - yaml
    - bash
    - java
    - docker
    - shell
    - json

extra_css:
  - css/extra.css
markdown_extensions:
  - admonition
  - toc:
      permalink: true
  - attr_list
  - def_list
  - footnotes
  - tables
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.details
  - pymdownx.snippets
  - pymdownx.superfences
  - mdx_gh_links:
      user: camalot
      repo: mkdocs-test
  - mkdocs-click
copyright: Copyright &copy; itzg 2024.
plugins:
  - search
  - autorefs
  - mkdocstrings:
      handlers:
        python:
          options:
            docstring_section_style: list
            members_order: source
            show_root_heading: true
            show_source: false
            show_signature_annotations: true
  # https://github.com/ultrabug/mkdocs-static-i18n
  - i18n:
      languages:
        - locale: en
          name: English
          build: true
          default: true
  - literate-nav:
      nav_file: README.md
      implicit_index: true
