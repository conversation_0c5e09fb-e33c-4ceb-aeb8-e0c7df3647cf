{"allow-flight": {"env": "ALLOW_FLIGHT"}, "allow-nether": {"env": "ALLOW_NETHER"}, "bug-report-link": {"env": "BUG_REPORT_LINK"}, "announce-player-achievements": {"env": "ANNOUNCE_PLAYER_ACHIEVEMENTS"}, "broadcast-console-to-ops": {"env": "BROADCAST_CONSOLE_TO_OPS"}, "broadcast-rcon-to-ops": {"env": "BROADCAST_RCON_TO_OPS"}, "difficulty": {"env": "DIFFICULTY"}, "enable-command-block": {"env": "ENABLE_COMMAND_BLOCK"}, "enable-jmx-monitoring": {"env": "ENABLE_JMX"}, "enable-query": {"env": "ENABLE_QUERY"}, "enable-rcon": {"env": "ENABLE_RCON"}, "enable-status": {"env": "ENABLE_STATUS"}, "enforce-secure-profile": {"env": "ENFORCE_SECURE_PROFILE"}, "enforce-whitelist": {"env": "ENFORCE_WHITELIST"}, "entity-broadcast-range-percentage": {"env": "ENTITY_BROADCAST_RANGE_PERCENTAGE"}, "force-gamemode": {"env": "FORCE_GAMEMODE"}, "function-permission-level": {"env": "FUNCTION_PERMISSION_LEVEL"}, "gamemode": {"env": "MODE"}, "generate-structures": {"env": "GENERATE_STRUCTURES"}, "generator-settings": {"env": "GENERATOR_SETTINGS"}, "hardcore": {"env": "HARDCORE"}, "hide-online-players": {"env": "HIDE_ONLINE_PLAYERS"}, "initial-disabled-packs": {"env": "INITIAL_DISABLED_PACKS"}, "initial-enabled-packs": {"env": "INITIAL_ENABLED_PACKS"}, "level-name": {"env": "LEVEL"}, "level-seed": {"env": "SEED"}, "level-type": {"env": "LEVEL_TYPE"}, "log-ips": {"env": "LOG_IPS"}, "max-build-height": {"env": "MAX_BUILD_HEIGHT"}, "max-chained-neighbor-updates": {"env": "MAX_CHAINED_NEIGHBOR_UPDATES"}, "max-players": {"env": "MAX_PLAYERS"}, "max-tick-time": {"env": "MAX_TICK_TIME"}, "max-world-size": {"env": "MAX_WORLD_SIZE"}, "motd": {"env": "MOTD"}, "network-compression-threshold": {"env": "NETWORK_COMPRESSION_THRESHOLD"}, "online-mode": {"env": "ONLINE_MODE"}, "op-permission-level": {"env": "OP_PERMISSION_LEVEL"}, "pause-when-empty-seconds": {"env": "PAUSE_WHEN_EMPTY_SECONDS"}, "player-idle-timeout": {"env": "PLAYER_IDLE_TIMEOUT"}, "prevent-proxy-connections": {"env": "PREVENT_PROXY_CONNECTIONS"}, "previews-chat": {"env": "PREVIEWS_CHAT"}, "pvp": {"env": "PVP"}, "query.port": {"env": "QUERY_PORT"}, "rcon.password": {"env": "RCON_PASSWORD"}, "rcon.port": {"env": "RCON_PORT"}, "region-file-compression": {"env": "REGION_FILE_COMPRESSION"}, "resource-pack": {"env": "RESOURCE_PACK"}, "resource-pack-id": {"env": "RESOURCE_PACK_ID"}, "resource-pack-prompt": {"env": "RESOURCE_PACK_PROMPT"}, "resource-pack-sha1": {"env": "RESOURCE_PACK_SHA1"}, "require-resource-pack": {"env": "RESOURCE_PACK_ENFORCE"}, "server-ip": {"env": "SERVER_IP"}, "server-name": {"env": "SERVER_NAME"}, "server-port": {"env": "SERVER_PORT"}, "simulation-distance": {"env": "SIMULATION_DISTANCE"}, "snooper-enabled": {"env": "SNOOPER_ENABLED"}, "spawn-animals": {"env": "SPAWN_ANIMALS"}, "spawn-monsters": {"env": "SPAWN_MONSTERS"}, "spawn-npcs": {"env": "SPAWN_NPCS"}, "spawn-protection": {"env": "SPAWN_PROTECTION"}, "sync-chunk-writes": {"env": "SYNC_CHUNK_WRITES"}, "use-native-transport": {"env": "USE_NATIVE_TRANSPORT"}, "view-distance": {"env": "VIEW_DISTANCE"}, "white-list": {"env": "WHITELIST_PROP"}}