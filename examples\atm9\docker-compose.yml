services:
  mc:
    image: itzg/minecraft-server
    ports:
      - "25565:25565"
    environment:
      EULA: "true"
      MODPACK_PLATFORM: AUTO_CURSEFORGE
      # Allocate API key from https://console.curseforge.com/
      # and set in .env file making sure to double up dollar signs, such as
      # CF_API_KEY=$$2a$$10$$....
      # Refer to https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#api-key
      CF_API_KEY: ${CF_API_KEY}
      CF_PAGE_URL: https://www.curseforge.com/minecraft/modpacks/all-the-mods-9
      # Optional: select a specific version/file
      #CF_FILENAME_MATCHER: "0.2.34"
      MEMORY: 4G
    volumes:
      - mc-data:/data

volumes:
  mc-data: {}