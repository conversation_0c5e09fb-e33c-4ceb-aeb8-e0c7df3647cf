services:
  mc:
    image: itzg/minecraft-server
    tty: true
    stdin_open: true
    ports:
      # Game port
      - "25565:25565/tcp"
      # Voice chat port
      - "24454:24454/udp"
    environment:
      EULA: "TRUE"
      TYPE: "FABRIC"
      VERSION: "1.21.1"
      # This will select the latest version of simple voice chat for 1.21.1.
      # You can specify a version by appending :versionID (e.g. simple-voice-chat:anabvqRL)
      MODRINTH_PROJECTS: "simple-voice-chat"
      MEMORY: 8G
    volumes:
      # attach the relative directory 'data' to the container's /data path
      - ./data:/data