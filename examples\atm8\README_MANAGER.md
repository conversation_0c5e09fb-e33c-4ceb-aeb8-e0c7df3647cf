# 🎮 Minecraft Server Manager

A comprehensive Python interface to view and manage players on your Minecraft server via RCON.

## ✨ Features

### 📊 **Command Line Interface**
- View server status and online players
- Get detailed player information (position, health, game mode)
- Manage players (kick, ban, unban, op, deop)
- Send messages to players
- View recent player activity
- Real-time server monitoring

### 🌐 **Web Interface**
- Beautiful Bootstrap-based dashboard
- Real-time server status monitoring
- Player management with detailed information
- Server console with live logs
- Quick action buttons
- Responsive design for mobile/desktop

### 🔧 **Management Features**
- **Player Actions**: Kick, ban, unban, teleport, give/remove OP
- **Server Monitoring**: Online players, server status, performance
- **Communication**: Send messages to all players or specific players
- **Activity Tracking**: Monitor player joins/leaves
- **Console Access**: View server logs and execute commands

## 🚀 Quick Start

### 1. **Setup**
```bash
# Install Python dependencies
python setup.py

# Or manually:
pip install Flask==2.3.3 Werkzeug==2.3.7
```

### 2. **Command Line Usage**
```bash
# View server status
python minecraft_manager.py status

# List online players with details
python minecraft_manager.py players --detailed

# Get specific player info
python minecraft_manager.py info RootCraft

# Kick a player
python minecraft_manager.py kick PlayerName --reason "Breaking rules"

# Ban a player
python minecraft_manager.py ban PlayerName --reason "Griefing"

# Send message to all players
python minecraft_manager.py say "Server restart in 5 minutes!"

# View recent activity
python minecraft_manager.py activity

# See all commands
python minecraft_manager.py --help
```

### 3. **Web Interface**
```bash
# Start the web server
python web_manager.py

# Access the interface
# Open browser to: http://localhost:5000
```

## 📱 Web Interface Screenshots

### Dashboard
- Server status overview
- Online player count
- Recent activity feed
- Quick actions panel
- Server information

### Players Page
- Detailed player cards with position, health, game mode
- Player management actions (kick, ban, teleport, OP)
- Whitelist and ban list management
- Real-time player information

### Console Page
- Live server logs
- Command execution interface
- Auto-refresh functionality
- Quick command buttons

## 🔧 Available Commands

### **Player Management**
| Command | Description | Example |
|---------|-------------|---------|
| `status` | Show server status | `python minecraft_manager.py status` |
| `players` | List online players | `python minecraft_manager.py players --detailed` |
| `info <player>` | Get player details | `python minecraft_manager.py info RootCraft` |
| `kick <player>` | Kick a player | `python minecraft_manager.py kick PlayerName` |
| `ban <player>` | Ban a player | `python minecraft_manager.py ban PlayerName` |
| `unban <player>` | Unban a player | `python minecraft_manager.py unban PlayerName` |
| `op <player>` | Give operator status | `python minecraft_manager.py op PlayerName` |
| `deop <player>` | Remove operator status | `python minecraft_manager.py deop PlayerName` |
| `say <message>` | Send message to all | `python minecraft_manager.py say "Hello everyone!"` |
| `activity` | Show recent activity | `python minecraft_manager.py activity` |

### **Web Interface Routes**
| Route | Description |
|-------|-------------|
| `/` | Main dashboard |
| `/players` | Player management |
| `/console` | Server console |
| `/api/status` | JSON API for status |
| `/api/players` | JSON API for players |

## 🔌 API Integration

The manager uses RCON to communicate with your Minecraft server. It automatically detects the Docker container and executes commands via `docker-compose exec mc rcon-cli`.

### **Requirements**
- Docker and docker-compose
- Minecraft server with RCON enabled (automatically configured)
- Python 3.6+
- Flask (for web interface)

## 🛠️ Configuration

The manager automatically works with your current Docker setup. No additional configuration needed!

### **Default Settings**
- **Server**: localhost:25565
- **RCON**: Enabled via Docker container
- **Web Interface**: localhost:5000
- **File Browser**: localhost:25580

## 📊 Player Information

The manager provides detailed player information:

### **Basic Info**
- Player name
- Online status
- Connection time

### **Game Data**
- Current position (X, Y, Z coordinates)
- Health points
- Game mode (Survival, Creative, Adventure, Spectator)
- Inventory (if accessible)

### **Server Data**
- Player count (online/max)
- Server performance
- Recent activity logs

## 🔒 Security Features

- **Offline Mode Support**: Works with cracked and premium accounts
- **Admin Controls**: Kick, ban, and operator management
- **Activity Monitoring**: Track player joins/leaves
- **Command Logging**: All actions are logged

## 🚨 Troubleshooting

### **Common Issues**

1. **"Connection failed"**
   - Ensure Minecraft server is running
   - Check Docker containers: `docker-compose ps`

2. **"Command timed out"**
   - Server might be overloaded
   - Try again in a few seconds

3. **"Permission denied"**
   - Ensure you have Docker permissions
   - Try running with `sudo` if needed

4. **Web interface not loading**
   - Check if port 5000 is available
   - Try a different port: `python web_manager.py --port 8080`

### **Debug Mode**
```bash
# Enable debug output
python minecraft_manager.py status --debug

# Web interface debug mode
python web_manager.py --debug
```

## 🎯 Use Cases

### **Server Administration**
- Monitor player activity
- Manage problematic players
- Send server announcements
- Track server performance

### **Community Management**
- Welcome new players
- Moderate chat and behavior
- Organize events
- Maintain server rules

### **Development & Testing**
- Test server configurations
- Monitor performance
- Debug player issues
- Automate server tasks

## 🔄 Updates & Maintenance

The manager is designed to work with your existing Minecraft server setup. Regular updates include:

- New Minecraft version compatibility
- Additional management features
- Performance improvements
- Security enhancements

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section
2. Verify your server is running: `docker-compose ps`
3. Test basic connectivity: `python minecraft_manager.py status`
4. Check server logs: `docker-compose logs mc`

---

**🎮 Happy server managing!** 

Your Minecraft server is now equipped with professional-grade management tools. Use responsibly and enjoy your enhanced server administration experience!
