#!/bin/bash
set -eu

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"

: "${QUILT_LAUNCHER:=}"
: "${QUILT_LAUNCHER_URL:=}"
: "${QUILT_INSTALLER_URL:=}"
: "${QUILT_INSTALLER_VERSION:=LATEST}"
: "${QUILT_LOADER_VERSION:=LATEST}"

isDebugging && set -x

function mc-image-helper-quilt(){
  mc-image-helper install-quilt \
    --loader-version="$QUILT_LOADER_VERSION" \
    --minecraft-version="$VERSION" \
    --output-directory=/data \
    --results-file="$resultsFile" "$@"
}

resultsFile=/data/.quilt.env

if [[ $QUILT_LAUNCHER ]]; then
  logWarning "Use of QUILT_LAUNCHER is a deprecated feature."
  SERVER="$QUILT_LAUNCHER"
  export SERVER
  resolveVersion

  export FAMILY=FABRIC
  exec "${SCRIPTS:-/}start-setupWorld" "$@"

elif [[ $QUILT_LAUNCHER_URL ]]; then
  logError "QUILT_LAUNCHER_URL is not longer supported. Pre-download and use QUILT_LAUNCHER."
  exit 2

elif [[ $QUILT_INSTALLER_URL ]]; then
  if ! mc-image-helper-quilt --installer-url="$QUILT_INSTALLER_URL"; then
    logError "Failed to installQuilt given custom installer URL $QUILT_INSTALLER_URL"
    exit 2
  fi

else
  if ! mc-image-helper-quilt --installer-version="$QUILT_INSTALLER_VERSION"; then
    logError "Failed to installQuilt given installer version $QUILT_INSTALLER_VERSION"
    exit 2
  fi

fi

applyResultsFile ${resultsFile}

export FAMILY=FABRIC
exec "${SCRIPTS:-/}start-setupWorld" "$@"
