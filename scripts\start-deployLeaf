#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-$(dirname "$0")}/start-utils"
set -o pipefail
set -e
isDebugging && set -x

resolveVersion
: "${LEAF_BUILD:=LATEST}"

# Docs at https://api.leafmc.one/docs/swagger-ui/index.html
leafApiUrl="https://api.leafmc.one/v2/projects/leaf"
leafDownloadsPage="https://www.leafmc.one/download"

if ! get --exists "${leafApiUrl}/versions/${VERSION}/builds"; then
  logError "Leaf builds do not exist for ${VERSION}"
  logError "       check ${leafDownloadsPage} for available versions"
  logError "       and set VERSION accordingly"
  exit 1
fi

if [[ "${LEAF_BUILD^^}" == "LATEST" ]]; then
  # Get the latest build number from the API, which will be the last object in the builds array
  if ! buildNumber=$(
    get --json-path '$.builds[-1].build' "${leafApiUrl}/versions/${VERSION}/builds"
  ); then
    logError "failed to list Leaf builds for ${VERSION}"
    exit 1
  fi
  LEAF_BUILD="${buildNumber}"
fi

if ! filename=$(
  get --json-path='$.downloads.primary.name' --json-value-when-missing="" "${leafApiUrl}/versions/${VERSION}/builds/${LEAF_BUILD}"
); then
  logError "Failed to retrieve download filename"
  exit 1
fi

SERVER="/data/$filename"

if ! get --skip-existing --log-progress-each -o "${SERVER}" "${leafApiUrl}/versions/${VERSION}/builds/${LEAF_BUILD}/downloads/${filename}"; then
  logError "Failed to download"
  exit 1
fi

export FAMILY=SPIGOT
export SERVER

exec "${SCRIPTS:-/}start-spiget" "$@"