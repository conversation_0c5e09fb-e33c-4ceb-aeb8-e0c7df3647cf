services:
  mc:
    image: itzg/minecraft-server
    environment:
      EULA: true
      MODPACK_PLATFORM: MODRINTH
      # NOTE: v36 doesn't startup correctly
      MODRINTH_MODPACK: https://modrinth.com/modpack/better-mc-forge-bmc4/version/v34.5
      MODRINTH_OVERRIDES_EXCLUSIONS: |
        config/paxi/datapacks/BE_default_endgen_fix*
      MODRINTH_FORCE_SYNCHRONIZE: true
      MEMORY: 4G
    ports:
      - "25565:25565"
    volumes:
      - mc-data:/data
      # or use a host directory binding
#      - ./data:/data
volumes:
  mc-data: