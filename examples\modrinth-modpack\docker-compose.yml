services:
  mc:
    image: itzg/minecraft-server
    tty: true
    stdin_open: true
    ports:
      - "25565:25565"
    environment:
      EULA: "TRUE"
      MODPACK_PLATFORM: MODRINTH
      MODRINTH_MODPACK: https://modrinth.com/modpack/cobblemon-fabric/version/1.3.2
      # or for auto-upgrading to latest
      # MODRINTH_MODPACK: https://modrinth.com/modpack/cobblemon-fabric
      # or just cobblemon-fabric or 5FFgwNNP
      # and could replace version URL with
      # MODRINTH_VERSION: nvrqJg44
      # MODRINTH_VERSION: 1.3.2
      # MODRINTH_VERSION: "Cobblemon [Fabric] 1.3.2"
    volumes:
      # attach the relative directory 'data' to the container's /data path
      - ./data:/data
