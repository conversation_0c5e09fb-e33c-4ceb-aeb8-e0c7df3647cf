<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="SysOut" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level]: %msg%n" />
        </Console>
        <Queue name="TerminalConsole">
            <PatternLayout pattern="[%d{HH:mm:ss} %level]: %msg%n" />
        </Queue>
        <RollingRandomAccessFile name="File" fileName="logs/latest.log" filePattern="logs/%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level]: %msg%n" />
            <Policies>
                <!-- Based on filePattern resolution, so daily -->
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="100 MB" />
                <OnStartupTriggeringPolicy />
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="logs">
                    <IfFileName glob="*.log.gz" />
                    <IfLastModified age="7d" />
                    <IfAccumulatedFileCount exceeds="20"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <Root level="info">
            <filters>
                <MarkerFilter marker="NETWORK_PACKETS" onMatch="DENY" onMismatch="NEUTRAL" />
            </filters>
            <AppenderRef ref="SysOut"/>
            <AppenderRef ref="File"/>
            <AppenderRef ref="TerminalConsole"/>
        </Root>
    </Loggers>
</Configuration>
