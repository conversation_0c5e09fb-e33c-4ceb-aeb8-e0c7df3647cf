#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"
isDebugging && set -x
set -o pipefail

resolveVersion
export SERVER="minecraft_server.${VERSION// /_}.jar"

if [ ! -e "$SERVER" ] || [ -n "$FORCE_REDOWNLOAD" ]; then
  debug "Finding version manifest for $VERSION"
  versionManifestUrl=$(get 'https://launchermeta.mojang.com/mc/game/version_manifest.json' | jq --arg VERSION "$VERSION" --raw-output '[.versions[]|select(.id == $VERSION)][0].url')
  result=$?
  if [ $result != 0 ]; then
    logError "Failed to obtain version manifest URL ($result)"
    exit 1
  fi
  if [ "$versionManifestUrl" = "null" ]; then
    logError "Couldn't find a matching manifest entry for $VERSION"
    exit 1
  fi
  debug "Found version manifest at $versionManifestUrl"

  if ! serverDownloadUrl=$(get --json-path '$.downloads.server.url' "${versionManifestUrl}"); then
    logError "Failed to obtain version manifest from $versionManifestUrl ($result)"
    exit 1
  elif [ "$serverDownloadUrl" = "null" ]; then
    logError "There is not a server download for version $VERSION"
    exit 1
  fi

  log "Downloading $VERSION server..."
  debug "Downloading server from $serverDownloadUrl"
  get -o "$SERVER" "$serverDownloadUrl"
  result=$?
  if [ $result != 0 ]; then
    logError "Failed to download server from $serverDownloadUrl ($result)"
    exit 1
  fi
fi
minecraftServerJarPath=/data/minecraft_server.jar

if versionLessThan 1.6; then
  if ! [[ -L $minecraftServerJarPath && $minecraftServerJarPath -ef "/data/$SERVER" ]]; then
    rm -f $minecraftServerJarPath
    ln -s "/data/$SERVER" $minecraftServerJarPath
  fi
  SERVER=minecraft_server.jar
elif [[ -L $minecraftServerJarPath ]]; then
  rm -f $minecraftServerJarPath
fi

isDebugging && ls -l
export FAMILY=VANILLA
exec "${SCRIPTS:-/}start-setupWorld" "$@"
