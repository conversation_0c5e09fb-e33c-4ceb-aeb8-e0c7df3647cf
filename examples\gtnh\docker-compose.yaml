services:
  mc:
    # make sure this java version matches with pack java version
    image: itzg/minecraft-server:java21
    tty: true
    stdin_open: true
    ports:
      - "25565:25565"
    environment:
      EULA: "TRUE"
      TYPE: CUSTOM
      GENERIC_PACKS: GT_New_Horizons_2.7.2_Server_Java_17-21
      GENERIC_PACKS_SUFFIX: .zip
      GENERIC_PACKS_PREFIX: https://downloads.gtnewhorizons.com/ServerPacks/
      # if this isn't true, then the container tries to download the modpack every run
      SKIP_GENERIC_PACK_UPDATE_CHECK: "true"
      MEMORY: 6G
      # Make sure that this matches what is in your pack's startserver bash file
      JVM_OPTS: -Dfml.readTimeout=180 @java9args.txt
      CUSTOM_JAR_EXEC: -jar lwjgl3ify-forgePatches.jar nogui
    volumes:
      # attach a managed volume, change to a relative or absolute host directory if needed
      - mc-data:/data
volumes:
  mc-data:
