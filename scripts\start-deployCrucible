#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-$(dirname "$0")}/start-utils"
set -o pipefail
set -e
isDebugging && set -x

resolveVersion
: "${CRUCIBLE_RELEASE:=latest}"

crucibleReleasesUrl=https://api.github.com/repos/CrucibleMC/Crucible/releases
if [[ ${CRUCIBLE_RELEASE^^} = LATEST ]]; then
  crucibleReleaseUrl=${crucibleReleasesUrl}/latest
else
  crucibleReleaseUrl=${crucibleReleasesUrl}/tags/${CRUCIBLE_RELEASE}
fi

if ! downloadUrl=$(get --json-path "$.assets[?(@.name =~ /Crucible-${VERSION}-.*\.jar/)].browser_download_url" \
    --accept "application/vnd.github.v3+json" "$crucibleReleaseUrl"); then
  logError "Failed to access ${CRUCIBLE_RELEASE} release of Crucible"
  exit 1
fi

if [[ $downloadUrl = null ]]; then
  logError "Failed to locate Crucible jar for $VERSION from ${CRUCIBLE_RELEASE}"
  exit 1
fi

log "Downloading Crucible from $downloadUrl"
if ! SERVER=$(get --skip-existing --output-filename -o /data "$downloadUrl"); then
  logError "Crucible jar from $downloadUrl"
  exit 1
fi

librariesDir=/data/libraries
if [ ! -d "$librariesDir" ]; then
  if ! librariesUrl=$(get --json-path "$.assets[?(@.name == 'libraries.zip')].browser_download_url" \
      --accept "application/vnd.github.v3+json" "$crucibleReleaseUrl"); then
    logError "Failed to access ${CRUCIBLE_RELEASE} release of Crucible for libraries"
    exit 1
  fi

  log "Downloading Crucible libraries"
  if ! get -o /tmp/libraries.zip "$librariesUrl"; then
    logError "Failed to download Crucible libraries from $librariesUrl"
    exit 1
  fi

  if ! unzip /tmp/libraries.zip -d "$librariesDir"; then
    logError "Failed to unzip Crucible libraries"
    exit 1
  fi
  rm /tmp/libraries.zip
fi

export SERVER
export FAMILY=HYBRID
export HYBRIDTYPE=forge

exec "${SCRIPTS:-/}start-spiget" "$@"
