#!/usr/bin/env python3
"""
Setup script for Minecraft Server Manager
"""

import subprocess
import sys
import os

def install_requirements():
    """Install Python requirements."""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def make_executable():
    """Make Python scripts executable."""
    scripts = ["minecraft_manager.py", "web_manager.py"]
    for script in scripts:
        if os.path.exists(script):
            os.chmod(script, 0o755)
            print(f"✅ Made {script} executable")

def test_connection():
    """Test connection to Minecraft server."""
    print("\nTesting connection to Minecraft server...")
    try:
        from minecraft_manager import MinecraftServerManager
        manager = MinecraftServerManager(".")
        status = manager.get_server_status()
        if status and status.get('players'):
            print(f"✅ Connected successfully! Players online: {status['players']['online_count']}")
            return True
        else:
            print("⚠️  Connected but no player data received")
            return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def main():
    print("🎮 Minecraft Server Manager Setup")
    print("=" * 40)
    
    # Install requirements
    if not install_requirements():
        print("Setup failed. Please install requirements manually.")
        return
    
    # Make scripts executable
    make_executable()
    
    # Test connection
    test_connection()
    
    print("\n🚀 Setup complete!")
    print("\nUsage:")
    print("  Command Line: python minecraft_manager.py --help")
    print("  Web Interface: python web_manager.py")
    print("  Then visit: http://localhost:5000")

if __name__ == "__main__":
    main()
