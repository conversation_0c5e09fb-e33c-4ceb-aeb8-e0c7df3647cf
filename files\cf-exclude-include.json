{"globalExcludes": ["advancement-plaques", "ambience-music-mod", "ambientsounds", "appleskin", "armor-chroma", "armor-toughness-bar", "audio-extension-for-fancymenu-forge", "auudio-forge", "beehivetooltips", "better-advancements", "better-foliage", "better-modlist-neoforge", "better-placement", "better-sprinting", "better-third-person", "better-tips-nbt-tag", "betterf3", "betterfps", "biomeinfo", "block-drops-jei-addon", "blur-forge", "cartography", "chattoggle", "cherished-worlds", "chunk-animator", "clickable-advancements", "compass-coords", "configured", "controllable", "controlling", "craftpresence", "ctm", "custom-main-menu", "dark-mode-everywhere", "defensive-measures", "ding", "drippy-loading-screen", "dynamic-surroundings", "dynamic-view", "dynamiclights-reforged", "easiervillagertrading", "effective-forge", "embeddium", "embeddium-extension", "embeddium-extras", "enchantment-descriptions", "enhanced-boss-bars", "enhancedvisuals", "entity-collision-fps-fix", "entity-model-features", "entity-texture-features-fabric", "entityculling", "equipment-compare", "essential-mod", "euphoria-patches", "extreme-sound-muffler", "ezzoom", "fading-night-vision", "falling-leaves-forge", "fancymenu", "farsight", "faster-ladder-climbing", "flerovium", "foamfix-optimization-mod", "forgeskyboxes", "fps-reducer", "free-cam", "ftb-backups-2", "fullscreen-windowed-borderless-for-minecraft", "hwyla", "iceberg", "ignitioncoil", "illager-raid-music", "inmisaddon", "iris-flywheel-compat", "irisshaders", "item-borders", "item-highlighter", "item-obliterator", "itemphysic-lite", "itemzoom", "just-enough-harvestcraft", "just-enough-mekanism-multiblocks", "just-enough-resources-jer", "just-zoom", "legendary-tooltips", "lighty", "loot-capacitor-tooltips", "loot-journal", "lootbeams", "magnesium-extras", "make-bubbles-pop", "menumobs", "minecraft-rich-presence", "model-gap-fix", "more-overlays", "mouse-tweaks", "neat", "nekos-enchanted-books", "no-nv-flash", "no-recipe-book", "not-enough-animations", "oculus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overloaded-armor-bar", "packmenu", "packmodemenu", "particular", "particular-reforged", "reauth", "rebind-narrator", "reblured", "reeses-sodium-options", "reforgium", "resource-reloader", "rubidium", "rubidium-extra", "ryoamiclights", "schematica", "seamless-loading-screen", "seamless-loading-screen-forge", "searchables", "<PERSON>hud", "shulkerboxviewer", "skin-layers-3d", "smart-hud", "smithing-template-viewer", "smooth-font", "smoothwater", "sodium", "sodium-extra", "sodium-options-api", "sodium-rubidium-occlusion-culling-fix", "sound", "sound-filters", "sound-physics-remastered", "sound-reloader", "stellar-sky", "swingthroughgrass", "textrues-embeddium-options", "textrues-rubidium-options", "thau<PERSON>-jei", "tips", "toast-control", "torohealth-damage-indicators", "true-darkness", "ungrab-mouse-mod", "vanillafix", "visuality", "waila-harvestability", "wakes-reforged", "wawla", "x<PERSON><PERSON><PERSON>", "yungs-menu-tweaks", "zume"], "modpacks": {"all-of-fabric-6": {"forceIncludes": ["revelationary"]}, "beyond-depth": {"forceIncludes": ["particular-reforged"]}, "create-arcane-engineering": {"forceIncludes": ["just-enough-resources-jer"]}, "skyfactory-5": {"forceIncludes": ["colored-torches", "dye-mob-dye", "openstairs"]}, "valhelsia-5": {"excludes": ["modernfix"]}}}