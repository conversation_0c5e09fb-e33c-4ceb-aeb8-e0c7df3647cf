services:
  mc:
    restart: "no"
    image: ${IMAGE_TO_TEST:-itzg/minecraft-server}
    environment:
      VANILLATWEAKS_FILE: /config/vt-datapacks.json,/config/vt-craftingtweaks.json
      EULA: "TRUE"
      SETUP_ONLY: "TRUE"
      # the following are only used to speed up test execution
      TYPE: CUSTOM
      CUSTOM_SERVER: /servers/fake.jar
      VERSION: 1.18.1
    volumes:
      - ./data:/data
      - ./vt-datapacks.json:/config/vt-datapacks.json:ro
      - ./vt-craftingtweaks.json:/config/vt-craftingtweaks.json:ro
      # the following are only used to speed up test execution
      - ./verify.sh:/servers/fake.jar
