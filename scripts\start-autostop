#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"

: "${SERVER_PORT:=25565}"
: "${ENABLE_AUTOSTOP:=false}"
: "${AUTOSTOP_TIMEOUT_EST:=3600}"
: "${AUTOSTOP_TIMEOUT_INIT:=1800}"
: "${AUTOSTOP_PERIOD:=10}"
: "${DEBUG_AUTOSTOP:=false}"
export SERVER_PORT
export ENABLE_AUTOSTOP
export AUTOSTOP_TIMEOUT_EST
export AUTOSTOP_TIMEOUT_INIT
export AUTOSTOP_PERIOD
export DEBUG_AUTOSTOP

log "Autostop functionality enabled"

isDebugging && set -x

isNumericElseSetToDefault "AUTOSTOP_PERIOD" 10
checkIfNotZeroElseSetToDefault "AUTOSTOP_PERIOD" 10
isNumericElseSetToDefault "AUTOSTOP_TIMEOUT_EST" 3600
isNumericElseSetToDefault "AUTOSTOP_TIMEOUT_INIT" 1800

/auto/autostop-daemon.sh &
