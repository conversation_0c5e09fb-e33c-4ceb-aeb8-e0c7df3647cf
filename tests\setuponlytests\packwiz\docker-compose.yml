services:
  web:
    image: nginx
    volumes:
      - ./web:/usr/share/nginx/html
  mc:
    depends_on:
      - web
    image: ${IMAGE_TO_TEST:-itzg/minecraft-server}
    environment:
      EULA: "true"
      PACKWIZ_URL: http://web/pack.toml
      TYPE: CUSTOM
      CUSTOM_SERVER: /servers/fake.jar
      VERSION: 1.19
      DEBUG_HELPER: "true"
    volumes:
      - ./data:/data
      - ./fake.jar:/servers/fake.jar
