name: Verify PR
on:
  pull_request:
    branches: [ master ]
    types: [assigned, opened, synchronize, labeled]
    paths-ignore:
      - "*.md"
      - "docs/**"
      - "examples/**"
      - "notes/**"
      - "kustomize/**"
      - "docker-compose*.yml"
      - "mkdocs.yml"

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        variant:
          - java21
          - java21-alpine
          - java17
          - java8
        include:
        # JAVA 21:
          - variant: java21
            baseImage: eclipse-temurin:21-jre
            platforms: linux/amd64,linux/arm64
            mcVersion: latest
          - variant: java21-alpine
            baseImage: eclipse-temurin:21-jre-alpine
            platforms: linux/amd64,linux/arm64
            mcVersion: latest
        # JAVA 17:
          - variant: java17
            # jammy doesn't work until minecraft updates to https://github.com/netty/netty/issues/12343
            baseImage: eclipse-temurin:17-jre-focal
            platforms: linux/amd64
            mcVersion: 1.20.4
          - variant: java8
            baseImage: eclipse-temurin:8u312-b07-jre-focal
            platforms: linux/amd64
            mcVersion: 1.12.2
            # Pin version for Java 8
            mcHelperVersion: 1.42.1
    env:
      IMAGE_TO_TEST: ${{ github.repository_owner }}/minecraft-server:test-${{ matrix.variant }}-${{ github.run_id }}
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.2
        with:
          # for build-files step
          fetch-depth: 0

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3.10.0

      - name: Confirm multi-arch build
        uses: docker/build-push-action@v6.18.0
        with:
          platforms: ${{ matrix.platforms }}
          # ensure latest base image is used
          pull: true
          build-args: |
            BASE_IMAGE=${{ matrix.baseImage }}
            ${{ matrix.mcHelperVersion && format('MC_HELPER_VERSION={0}', matrix.mcHelperVersion) }}
          cache-from: type=gha,scope=${{ matrix.variant }}

      - name: Build for test
        uses: docker/build-push-action@v6.18.0
        with:
          # Only build single platform since loading multi-arch image into daemon fails with
          # "docker exporter does not currently support exporting manifest lists"
          platforms: linux/amd64
          tags: ${{ env.IMAGE_TO_TEST }}
          # ensure latest base image is used
          pull: true
          # load into daemon for test usage in next step
          load: true
          push: false
          build-args: |
            BASE_IMAGE=${{ matrix.baseImage }}
            ${{ matrix.mcHelperVersion && format('MC_HELPER_VERSION={0}', matrix.mcHelperVersion) }}
          cache-from: type=gha,scope=${{ matrix.variant }}

      - name: Run tests
        env:
          MINECRAFT_VERSION: ${{ matrix.mcVersion }}
          VARIANT: ${{ matrix.variant }}
          CF_API_KEY: ${{ secrets.CF_API_KEY }}
          DEBUG: ${{ runner.debug }}
        run: |
          tests/test.sh
