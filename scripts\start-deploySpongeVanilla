#!/bin/bash

# shellcheck source=start-utils
. ${SCRIPTS:-/}start-utils

export TYPE=spongevanilla
: ${SPONGEBRANCH:=STABLE}
: ${SPONGEVERSION:=}

# Parse branch
log "Choosing branch for Sponge"
case "$SPONGEBRANCH" in

  EXPERIMENTAL|experimental|BLEEDING|bleeding)
    SPONGEBRANCH=bleeding
  ;;

  *)
    SPONGEBRANCH=stable
  ;;

esac

# If not SPONGEVERSION selected, detect last version on selected branch
if [ -z $SPONGEVERSION ]; then
  log "Choosing Version for Sponge"
  SPONGEVERSION=$(curl -fsSL https://dl-api.spongepowered.org/v1/org.spongepowered/$TYPE | jq -r --arg SPONGEBRANCH "$SPONGEBRANCH" '.buildTypes.$SPONGEBRANCH.latest.version')
fi

VERSION="$SPONGEVERSION"
export VERSION
export SERVER="spongevanilla-$SPONGEVERSION.jar"

if [ ! -e "$SERVER" ] || [ -n "$FORCE_REDOWNLOAD" ]; then
  log "Downloading $SERVER ..."
  curl -sSL -o "$SERVER" "https://repo.spongepowered.org/maven/org/spongepowered/$TYPE/$SPONGEVERSION/$SERVER"
fi

export FAMILY=SPONGE
exec "${SCRIPTS:-/}start-setupWorld" "$@"
