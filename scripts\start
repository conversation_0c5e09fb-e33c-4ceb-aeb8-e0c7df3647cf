#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"

# The Dockerfile ENVs take precedence here, but defaulting for testing consistency
: "${UID:=1000}"
: "${GID:=1000}"
: "${SKIP_CHOWN_DATA:=false}"

umask "${UMASK:=0002}"

# Remove from previous run and do this as elevated user since file used to be created before demoting
rm -f "$HOME/.rcon-cli.env"

if ! isTrue "${SKIP_SUDO:-false}" && [ "$(id -u)" = 0 ]; then
  runAsUser=minecraft
  runAsGroup=minecraft

  if [[ -v UID ]]; then
    if [[ $UID != 0 ]]; then
      if [[ $UID != $(id -u minecraft) ]]; then
        log "Changing uid of minecraft to $UID"
        usermod -u $UID minecraft
      fi
    else
      runAsUser=root
    fi
  fi

  if [[ -v GID ]]; then
    if [[ $GID != 0 ]]; then
      if [[ $GID != $(id -g minecraft) ]]; then
        log "Changing gid of minecraft to $GID"
        groupmod -o -g "$GID" minecraft
      fi
    else
      runAsGroup=root
    fi
  fi

  if isFalse "${SKIP_CHOWN_DATA}" && [[ $(stat -c "%u" /data) != "$UID" ]]; then
    log "Changing ownership of /data to $UID ..."
    chown -R ${runAsUser}:${runAsGroup} /data
  fi

  if [[ ${SKIP_NSSWITCH_CONF^^} != TRUE ]]; then
    echo 'hosts: files dns' > /etc/nsswitch.conf
  fi

  exec $(getSudoFromDistro) ${runAsUser}:${runAsGroup} "${SCRIPTS:-/}start-configuration" "$@"
else
  exec "${SCRIPTS:-/}start-configuration" "$@"
fi
