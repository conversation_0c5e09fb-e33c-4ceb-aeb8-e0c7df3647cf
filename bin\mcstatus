#!/bin/bash

warning "mcstatus is deprecated; calling mc-monitor instead"

##### mcstatus shim for mc-monitor
# handles translating calls to
#   mcstatus (host:port) (command)
# where the actual command is ignore, but is typically ping or status

addr="$1"

IFS=':'
read -a parts <<< "${addr}"
args=(--host ${parts[0]}) 
if [[ ${#parts[*]} -gt 1 ]]; then
    args+=(--port ${parts[1]})
fi
exec mc-monitor ${args[@]}