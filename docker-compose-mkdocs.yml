# This composition can be used to serve up the rendered mkdocs for local authoring.
#
# docker compose -f docker-compose-mkdocs.yml -p mkdocs up
#
# and then access http://localhost:8000

services:
  mkdocs:
    build:
      context: .
      dockerfile: docs/Dockerfile
    volumes:
      - ./mkdocs.yml:/mkdocs/mkdocs.yml
      - ./docs:/mkdocs/docs
    command:
      - serve
      - --dev-addr=0.0.0.0:8000
    ports:
      - "8000:8000"