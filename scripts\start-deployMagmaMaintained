#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"
isDebugging && set -x

: "${FORGE_VERSION:=}"
: "${MAGMA_MAINTAINED_TAG:=}"

resolveVersion

if [ -z $MAGMA_MAINTAINED_TAG ]; then
  logError "The variable MAGMA_MAINTAINED_TAG is not specified"
  exit 1
fi

# Magma-1.12.2/releases/download/88659fb/Magma-1.12.2-88659fb-server.jar
# Magma-1.20.1/releases/download/adec9ce/magma-1.20.1-47.2.17-adec9ce-server.jar
if [[ ${VERSION} = "1.12.2" ]]; then
  fileName="Magma-${VERSION}-${MAGMA_MAINTAINED_TAG}-server.jar"
else
  if [ -z $FORGE_VERSION ]; then
    logError "The variable FORGE_VERSION is not specified"
    exit 1
  fi
  fileName="magma-${VERSION}-${FORGE_VERSION}-${MAGMA_MAINTAINED_TAG}-server.jar"
fi
downloadUrl="https://github.com/magmamaintained/Magma-${VERSION}/releases/download/${MAGMA_MAINTAINED_TAG}/${fileName}"

if ! SERVER=$(get --output-filename --skip-up-to-date --output /data "$downloadUrl"); then
  logError "Failed to download Magma Maintained server jar from $downloadUrl"
  exit 1
fi

export SERVER
export FAMILY=HYBRID
export HYBRIDTYPE=forge

exec "${SCRIPTS:-/}start-spiget" "$@"
