services:
  mc:
    image: itzg/minecraft-server
    ports:
      # expose the Minecraft server port outside of container
      - "25565:25565"
    environment:
      # REQUIRED for all types
      EULA: "TRUE"
      # Set server type (vs the default of vanilla)
      TYPE: FTBA
      # Use Pack ID from https://www.feed-the-beast.com/modpacks/119-ftb-presents-direwolf20-120?tab=about
      FTB_MODPACK_ID: "119"
#      FTB_MODPACK_VERSION_ID: ""
      MEMORY: 4G
    volumes:
      # use a named, managed volume for data volume
      - data:/data

volumes:
  # declared the named volume, but use default/local storage engine
  data: {}
