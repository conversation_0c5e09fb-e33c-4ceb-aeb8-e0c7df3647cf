{"globalExcludes": ["3dskinlayers", "ae2-emi-crafting", "AmbientSounds", "amecs", "Animation_Overhaul", "appleskin", "au<PERSON>o", "axolotlbuckets", "BadOptimizations", "BetterAdvancements", "betterbeds", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BHMenu", "blur", "Boat-Item-View", "bobby", "cat_jam", "chat_heads", "chatanimation", "cherishedworlds", "citresewn", "clickadv", "compass-coords", "connectedness", "connector", "craftpresence", "cwb", "DisableCustomWorldsAdvice", "drippyloadingscreen", "eating-animation", "emiffect", "emitrades", "entity_model_features", "entity_texture_features", "entityculling", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallingleaves", "fancymenu", "fast-ip-ping", "FauxCustomEntityData", "feytweaks", "figura", "GeckoLibIrisCompat", "gpumemleakfix", "Highlighter", "ImmediatelyFast", "indium", "iris", "iris-flywheel", "ItemBorders", "ItemLocks", "justzoom", "language-reload", "lazy-language-loader", "LegendaryTooltips", "loadmyresources", "lootbeams", "MindfulDarkness", "MouseTweaks", "nicer-skies", "notenoughanimations", "oculus", "OverflowingBars", "PickUpNotifier", "PresenceFootsteps", "Prism", "reforgium", "reeses_sodium_options", "ResourcePackOverrides", "ryoamiclights", "screenshot_viewer", "Searchables", "<PERSON>hud", "ShoulderSurfing", "skinlayers3d", "sodium", "sorted_enchantments", "visuality", "VR-Combat", "Ye<PERSON>Exper<PERSON><PERSON>", "yungsmenutweaks", "Zoomify", "zume"], "globalForceIncludes": [], "modpacks": {}}