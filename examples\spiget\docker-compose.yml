# NOTE
# This file is purposely named spiget with an "e" since it provides an example of the
# feature https://docker-minecraft-server.readthedocs.io/en/latest/mods-and-plugins/spiget/
# which uses the Spiget API at https://spiget.org/

services:
  mc:
    image: itzg/minecraft-server
    ports:
      - "25565:25565"
    environment:
      EULA: "TRUE"
      TYPE: PAPER
      SPIGET_RESOURCES: 34315,3836
      REMOVE_OLD_MODS: true
    volumes:
      - ./data:/data
