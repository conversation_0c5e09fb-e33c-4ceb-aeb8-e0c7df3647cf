#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"

set -euo pipefail
isDebugging && set -x

IFS=$'\n\t'

if versionLessThan 1.17; then
  logError "Pufferfish server type only supports versions 1.17, 1.18 or 1.19, use PUFFERFISH_BUILD to select the the correct build 47 => 1.18.1, 50 => 1.18.2 etc"
  exit 1
fi

: "${PUFFERFISH_BUILD:=lastSuccessfulBuild}"

majorVersion=$(get_major_version "$VERSION")
PUFFERFISH_BUILD_JSON=$(curl -X GET -s "https://ci.pufferfish.host/job/Pufferfish-${majorVersion}/${PUFFERFISH_BUILD}/api/json")
# Example: "url": "https://ci.pufferfish.host/job/Pufferfish-1.18/50/",
PUFFERFISH_BUILD_URL=$(jq -n "$PUFFERFISH_BUILD_JSON" | jq -jc '.url // empty' )
# Example: "fileName": "pufferfish-paperclip-1.18.2-R0.1-SNAPSHOT-reobf.jar",
PUFFERFISH_BUILD_FILENAME=$(jq -n "$PUFFERFISH_BUILD_JSON" | jq -jc '.artifacts[].fileName // empty' )
PUFFERFISH_BUILD_DOWNLOAD_URL="${PUFFERFISH_BUILD_URL}artifact/build/libs/${PUFFERFISH_BUILD_FILENAME}"

# Setting server to the Jar filename for export.
export SERVER=$PUFFERFISH_BUILD_FILENAME

log "Removing old Pufferfish versions ..."
shopt -s nullglob
for f in pufferfish-*.jar; do
  [[ $f != "$SERVER" ]] && rm "$f"
done

if [[ ! -f "$SERVER" ]] || isTrue "${FORCE_REDOWNLOAD:-false}"; then
    log "Downloading Pufferfish from $PUFFERFISH_BUILD_DOWNLOAD_URL ..."
    if ! get -o "$SERVER" "$PUFFERFISH_BUILD_DOWNLOAD_URL"; then
      logError "Failed to download from $PUFFERFISH_BUILD_DOWNLOAD_URL (status=$?)"
      exit 3
    fi
fi

# Normalize on Spigot for later operations
export FAMILY=SPIGOT

exec "${SCRIPTS:-/}start-spiget" "$@"
