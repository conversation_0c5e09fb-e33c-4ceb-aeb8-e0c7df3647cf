{% extends "base.html" %}

{% block title %}Dashboard - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row g-4">
    <div class="col-lg-8">
        <div class="card glass-intense animate__animated animate__fadeInUp">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>Server Status
                    <span class="badge bg-success ms-2 animate__animated animate__pulse animate__infinite" style="font-size: 0.7rem;">LIVE</span>
                </h5>
            </div>
            <div class="card-body">
                {% if status %}
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-4 glass p-3 rounded-3 animate__animated animate__fadeInLeft">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-20 rounded-circle p-3 position-relative">
                                    <i class="fas fa-circle status-{{ status.status }} fs-4"></i>
                                    <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%); animation: pulse 2s infinite;"></div>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-white">Server Status</h6>
                                <p class="mb-0 text-white-50">{{ status.status.title() }}</p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-4 glass p-3 rounded-3 animate__animated animate__fadeInLeft animate__delay-1s">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-20 rounded-circle p-3 position-relative">
                                    <i class="fas fa-users text-primary fs-4"></i>
                                    <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(99, 102, 241, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 0.5s;"></div>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-white">Players Online</h6>
                                <p class="mb-0">
                                    <span class="fs-4 fw-bold text-primary" id="online-count">{{ status.players.online_count }}</span>
                                    <span class="text-white-50">/ <span id="max-players">{{ status.players.max_players }}</span></span>
                                </p>
                            </div>
                        </div>

                        {% if status.players.players %}
                        <div class="mb-3 glass p-3 rounded-3 animate__animated animate__fadeInUp animate__delay-2s">
                            <h6 class="mb-2 text-white">Currently Online</h6>
                            <div class="d-flex flex-wrap gap-2">
                                {% for player in status.players.players %}
                                <span class="badge bg-success border border-success animate__animated animate__fadeIn" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                                    <i class="fas fa-user me-1"></i>{{ player }}
                                </span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-4">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-clock text-info fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Last Updated</h6>
                                <p class="mb-0 text-muted last-refresh">{{ status.timestamp.split('T')[1].split('.')[0] }}</p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-4">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-tachometer-alt text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Performance</h6>
                                <p class="mb-0 text-muted">
                                    {% if status.tps and 'not available' not in status.tps %}
                                        {{ status.tps }}
                                    {% else %}
                                        Monitoring...
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-secondary bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-memory text-secondary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Memory</h6>
                                <p class="mb-0 text-muted">4GB Allocated</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-warning border-0 bg-warning bg-opacity-10 animate__animated animate__shakeX">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle text-warning fs-4 me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Connection Issue</h6>
                            <p class="mb-0">Unable to connect to server. Please check if the server is running.</p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card glass-intense mt-4 animate__animated animate__fadeInUp animate__delay-1s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullhorn me-2"></i>Broadcast Message
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('send_message') }}" onsubmit="handleFormSubmit(this, 'Message sent successfully!')">
                    <div class="mb-3">
                        <label for="messageInput" class="form-label">Message</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="messageInput" name="message" placeholder="Enter your message to all players..." required>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-paper-plane me-2"></i>Send to All Players
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card glass-intense animate__animated animate__fadeInRight">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Recent Activity
                    <button class="btn btn-sm btn-outline-primary ms-auto" onclick="refreshActivity()" data-bs-toggle="tooltip" title="Refresh activity">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </h5>
            </div>
            <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                {% if recent_activity %}
                    {% for activity in recent_activity %}
                    <div class="activity-item activity-{{ activity.action }} animate__animated animate__fadeInUp" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-{{ 'sign-in-alt text-success' if activity.action == 'joined' else 'sign-out-alt text-danger' }}"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-medium">{{ activity.player }}</div>
                                <small class="text-muted">{{ activity.action }} at {{ activity.time }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No recent activity</p>
                    <small class="text-muted">Player joins and leaves will appear here</small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card glass-intense mt-4 animate__animated animate__fadeInRight animate__delay-1s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('players') }}" class="btn btn-outline-primary d-flex align-items-center">
                        <i class="fas fa-users me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Manage Players</div>
                            <small class="text-muted">View, kick, ban, and manage players</small>
                        </div>
                    </a>
                    <a href="{{ url_for('console') }}" class="btn btn-outline-secondary d-flex align-items-center">
                        <i class="fas fa-terminal me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Server Console</div>
                            <small class="text-muted">View logs and execute commands</small>
                        </div>
                    </a>
                    <a href="http://localhost:25580" target="_blank" class="btn btn-outline-info d-flex align-items-center">
                        <i class="fas fa-folder me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">File Manager</div>
                            <small class="text-muted">Browse and edit server files</small>
                        </div>
                    </a>
                    <button class="btn btn-outline-success d-flex align-items-center" onclick="refreshData(); ToastManager.show('Data refreshed successfully!', 'success')">
                        <i class="fas fa-sync-alt me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Refresh Status</div>
                            <small class="text-muted">Update server information</small>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-2s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Server Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-server text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Server Address</h6>
                                <code class="text-muted">localhost:25565</code>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('localhost:25565')" data-bs-toggle="tooltip" title="Copy address">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-cube text-success"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Version</h6>
                                <span class="badge bg-success-subtle text-success">Minecraft 1.21.4</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-unlock text-warning"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Mode</h6>
                                <span class="badge bg-warning-subtle text-warning">Offline (Cracked)</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-folder text-info"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">File Browser</h6>
                                <a href="http://localhost:25580" target="_blank" class="text-decoration-none">
                                    <code>localhost:25580</code>
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        ToastManager.show('Address copied to clipboard!', 'success');
    }, function(err) {
        ToastManager.show('Failed to copy address', 'error');
    });
}

function refreshActivity() {
    // Add loading animation to the refresh button
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    setTimeout(() => {
        icon.classList.remove('fa-spin');
        ToastManager.show('Activity refreshed!', 'info');
    }, 1000);
}
</script>
{% endblock %}
{% endblock %}
