services:
  mc:
    image: itzg/minecraft-server:java8
    volumes:
    - data:/data
    - ./modpacks:/modpacks:ro
    environment:
      EULA: "true"
      TYPE: "FORGE"
      VERSION: "1.12.2"
      FORGE_VERSION: "14.23.5.2860"
      OVERRIDE_SERVER_PROPERTIES: "true"
      DIFFICULTY: "hard"
      MAX_TICK_TIME: "-1"
      ALLOW_FLIGHT: "true"
      ENABLE_COMMAND_BLOCK: "true"
      VIEW_DISTANCE: "10"
      MEMORY: "4G"
      # Download from https://www.curseforge.com/minecraft/modpacks/rlcraft and place in modpacks subdir
      GENERIC_PACK: "/modpacks/RLCraft+Server+Pack+1.12.2+-+Release+v2.9.1c.zip"
    ports:
      - 25565:25565

volumes:
  data:
