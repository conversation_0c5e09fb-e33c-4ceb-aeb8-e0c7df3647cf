name: Discord notifications

on:
  release:
    types:
      - published
  workflow_run:
    workflows: ["Build and Publish"]
    types:
      - completed

env:
  DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}

jobs:
  discord:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: on-publish
        if: github.event_name == 'release' && github.event.action == 'published'
        uses: SethCohen/github-releases-to-discord@v1.16.2
        with:
          webhook_url: ${{ secrets.DISCORD_WEBHOOK }}
      - name: on-success
        if: github.event.workflow_run.conclusion == 'success'
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Github repo: ${{ github.repository }}
            - Branch: ${{ github.event.workflow_run.head_branch }}
            - [Link: to Actions](<${{ github.event.workflow_run.html_url }}>)
            - Status: 🎉  ${{ github.event.workflow_run.conclusion }}  🍏
      - name: on-failure
        if: github.event.workflow_run.conclusion == 'failure'
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Github repo: ${{ github.repository }}
            - Branch: ${{ github.event.workflow_run.head_branch }}
            - [Link: to Actions](<${{ github.event.workflow_run.html_url }}>)
            - Status: 🤔  ${{ github.event.workflow_run.conclusion }}  💣💥
