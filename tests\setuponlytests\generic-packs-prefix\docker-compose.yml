services:
  mc:
    image: ${IMAGE_TO_TEST:-itzg/minecraft-server}
    environment:
      EULA: "true"
      SETUP_ONLY: "TRUE"
      GENERIC_PACKS: testing
      GENERIC_PACKS_PREFIX: /packs/
      GENERIC_PACKS_SUFFIX: .zip
      DEBUG: "true"
      # the following are only used to speed up test execution
      TYPE: CUSTOM
      CUSTOM_SERVER: /servers/fake.jar
      VERSION: 1.18.1
    volumes:
      - ./packs:/packs
      - ./data:/data
      # the following are only used to speed up test execution
      - ./verify.sh:/servers/fake.jar
