####################################################################
#                         CURSEFORGE                               #
#                                                                  #
# Date: 20221005                                                   #
#                                                                  #
# Mod: All The Mods 7 0.4.32                                       #
#                                                                  #
# Notes: Verify that there is no EULA file in the modpack.zip      #
#        if you do not delete it the EULA flag below will be       #
#        overwritten when the modpack is copied.                   #
#                                                                  #
####################################################################
services:

####################################################################
#                         Service Name                             #
#                                                                  #
# Define Service Name here. If using RCON this name will be        #
# referenced again as RWA_RCON_HOST below.                         #
#                                                                  #
# Example: 'name:' or 'mc_atm6:'                                   #
####################################################################
  mc_atm7:

####################################################################
#                      Image & Container Name                      #
#                                                                  #
# Specify Image Name and Java Version. The 'image' will always be  #
# 'itzg/minecraft-server' however the tag added to the end is      #
# where you can specify the java version or container architecture.#
# See readme.md for a full list.                                   #
#                                                                  #
# 'container_name:' This can be anything you like. This is the name#
# that will show when you run 'docker ps' commands.                #
####################################################################
    image: itzg/minecraft-server
    container_name: mc_atm7

####################################################################
#                         Server Ports                             #
#                                                                  #
# Specify external port.                                           #
####################################################################
    ports:
      - 25565:25565

####################################################################
#                   Automatic Server Restart                       #
#                                                                  #
# Define a restart policy here.                                    #
#    - 'no' = Do not restart.                                      #
#    - 'on-failure' = Restart if container exits because an error. #
#    - 'always' = Regardless of stop reason.                       #
#    - 'unless-stopped' = Similar to always except if stopped.     #
####################################################################
    restart: unless-stopped

####################################################################
#                     Volume and Folder Access                     #
#                                                                  #
# This section defines what folders and volumes you want to give   #
# this container access to. It is recommended to leaves these set  #
# to the default values unless you know what you are doing.        #
#                                                                  #
# Place your mod zip file in a folder called 'modpacks' in the     #
# same directory you place this docker-compose file.               #
#                                                                  #
# Specify the data volume name or directory here as well.          #
# In this example the volume name is 'data'. When docker creates   #
# the volume it will add what ever name you give it here to the    #
# end of the container name specified above. In this example it    #
# would be named 'mc_atm6_data'. If you change this be sure to     #
# update the volume name at the bottom of this config.             #
####################################################################
    volumes:
      - ./modpacks:/modpacks:ro
      - data:/data

####################################################################
#                             EULA                                 #
#                                                                  #
# Accept EULA by setting to "true"                                 #
####################################################################
    environment:
      EULA: "true"

####################################################################
#                       CURSEFORGE INSTALL                         #
#                                                                  #
# Sets install type to FORGE and specifys the zip folder name      #
# and location of your mod pack.                                   #
#                                                                  #
# TYPE: Defines the install type as CURSEFORGE                     #
#                                                                  #
# CF_SERVER_MOD: Define where the modpack.zip is located.          #
#                                                                  #
# Place your mod zip file in a folder called 'modpacks' in the     #
# same directory you place this docker-compose file.               #
####################################################################
      TYPE: CURSEFORGE
      CF_SERVER_MOD: /modpacks/ATM7-0.4.32-server.zip

####################################################################
#                          Server Memory                           #
#                                                                  #
# Set Maximum amount of memory allowed for your server.            #
####################################################################
      MEMORY: "8G"

####################################################################
#                         Logging Options                          #
#                                                                  #
# Set to "true" to delete old logs                                 #
####################################################################
      ENABLE_ROLLING_LOGS: "true"

####################################################################
#                          Server Timezone                         #
#                                                                  #
# Specify server Timezone                                          #
####################################################################
      TZ: "America/New_York"

####################################################################
#                       Minecraft Game Options                     #
#                                                                  #
# List any game options you want to define here. A full list can   #
# be found on the readme.md page on github.                        #
####################################################################
      OVERRIDE_SERVER_PROPERTIES: "true"
      DIFFICULTY: "easy"
      MAX_TICK_TIME: "-1"
      ALLOW_FLIGHT: "true"
      OPS: ""
      VIEW_DISTANCE: 10
      MAX_PLAYERS: 10
      PVP: "false"
      LEVEL_TYPE: "biomesoplenty"
      MOTD: "Welcome Home"

####################################################################
#                            Volumes                               #
#                                                                  #
# Define data volume name here. You should leave this set to the   #
# default.                                                         #
####################################################################
volumes:
  data:
