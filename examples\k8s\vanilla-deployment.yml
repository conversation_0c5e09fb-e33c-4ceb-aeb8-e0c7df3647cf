apiVersion: v1
kind: Service
metadata:
  name: mc-vanilla
spec:
  type: NodePort
  ports:
  - port: 25565
    nodePort: 30000
  selector:
    app: mc-vanilla
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mc-vanilla
spec:
  selector:
    matchLabels:
      app: mc-vanilla
  template:
    metadata:
      labels:
        app: mc-vanilla
    spec:
      containers:
      - image: itzg/minecraft-server
        name: mc-vanilla
        env:
          # Use secret in real usage
        - name: EULA
          value: "true"
        ports:
        - containerPort: 25565
          name: main
        readinessProbe:
          exec:
            command: [ "/usr/local/bin/mc-monitor", "status", "--host", "localhost" ]
          # Give it i + p * f seconds to be ready, so 120 seconds
          initialDelaySeconds: 20
          periodSeconds: 5
          failureThreshold: 20
        # Monitor ongoing liveness
        livenessProbe:
          exec:
            command: ["/usr/local/bin/mc-monitor", "status", "--host", "localhost"]
          initialDelaySeconds: 120
          periodSeconds: 60
        volumeMounts:
        - name: mc-data
          mountPath: /data
      volumes:
      - name: mc-data
        emptyDir: {}
