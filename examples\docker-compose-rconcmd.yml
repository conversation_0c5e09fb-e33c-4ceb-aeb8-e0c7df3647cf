services:
  minecraft:
    image: ${IMAGE_TO_TEST:-itzg/minecraft-server}
    ports:
      - "25565:25565"
    volumes:
      - "mc:/data"
    environment:
      EULA: "TRUE"
      TYPE: FABRIC
      MEMORY: "2G"
      CURSEFORGE_FILES: |
        fabric-api
        chunky-pregenerator
      # Allocate API key from https://console.curseforge.com/
      # and set in .env file making sure to double up dollar signs, such as
      # CF_API_KEY=$$2a$$10$$....
      # Refer to https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#api-key
      CF_API_KEY: ${CF_API_KEY}
      # YAML Heredoc, be sure to use '|-' this will remove the first newline and final new line.
      # This is versus '|' that will leaving with two empty strings at top and bottom.
      RCON_CMDS_STARTUP:  |-
        /gamerule doFireTick false
        /team add New
        /team add Old
        /chunky radius 1000
        /chunky start
      RCON_CMDS_ON_CONNECT: |-
        /team join New @a[team=]
        /give @a[team=New] birch_boat
        /team join Old @a[team=New]
      RCON_CMDS_FIRST_CONNECT: |-
        /chunky pause
      RCON_CMDS_LAST_DISCONNECT: |-
        /kill @e[type=minecraft:boat]
        /chunky continue
    restart: unless-stopped
volumes:
  mc: {}
