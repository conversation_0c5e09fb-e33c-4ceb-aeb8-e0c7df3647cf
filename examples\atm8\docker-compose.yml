services:
  mc:
    image: itzg/minecraft-server:java21
    ports:
      - "25565:25565"
    environment:
      EULA: "TRUE"
      TYPE: "VANILLA"
      VERSION: "1.21.4"
      MEMORY: 4G
      ONLINE_MODE: "FALSE"
      ACCEPT_EULA: "TRUE"
    volumes:
      - mc-data:/data
      - ./eula.txt:/data/eula.txt
  # Since Docker's default volume driver creates volumes owned by root, this
  # init container will change ownership to match final UID of mc service, above
  init-filebrowser:
    image: filebrowser/filebrowser
    entrypoint: sh -c
    command:
      - "chown -R 1000: /database"
    restart: no
    volumes:
      - filebrowser-db:/database
  filebrowser:
    image: filebrowser/filebrowser
    depends_on:
      init-filebrowser:
        condition: service_completed_successfully
    user: "1000:1000"
    environment:
      FB_DATABASE: /database/filebrowser.db
    volumes:
      # Default FB_ROOT is /srv
      # In this example, the left-side needs to be the same as /data volume of mc service
      - mc-data:/srv
      - filebrowser-db:/database
    ports:
      - "25580:80"

volumes:
  mc-data: {}
  filebrowser-db: {}