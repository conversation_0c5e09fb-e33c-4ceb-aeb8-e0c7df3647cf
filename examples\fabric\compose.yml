services:
  mc:
    image: itzg/minecraft-server
    environment:
      EULA: "true"
      TYPE: FABRIC
#      VERSION: 1.21.4
#      FABRIC_INSTALLER_VERSION: 1.0.1
#      FABRIC_LOADER_VERSION: 0.16.10
      # Since Fabric server type only includes the loader, most times
      # the fabric-api is required for other mods to function
      MODRINTH_PROJECTS: |
        fabric-api
    ports:
      - "25565:25565"
    volumes:
      - fabric:/data

volumes:
  fabric: {}