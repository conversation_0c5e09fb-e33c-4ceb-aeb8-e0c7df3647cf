services:
  mc:
    image: itzg/minecraft-server:java8-multiarch
    environment:
      EULA: true
      MODPACK_PLATFORM: AUTO_CURSEFORGE
      # Allocate API key from https://console.curseforge.com/
      # and set in .env file making sure to double up dollar signs, such as
      # CF_API_KEY=$$2a$$10$$....
      # Refer to https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#api-key
      CF_API_KEY: ${CF_API_KEY}
      CF_SLUG: rlcraft
      CF_FILENAME_MATCHER: 2.9.3
      CF_FORCE_SYNCHRONIZE: true
      MEMORY: 4G
    volumes:
      - ./data:/data
    ports:
      - "25565:25565"
