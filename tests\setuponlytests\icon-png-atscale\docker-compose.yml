services:
  web:
    image: nginx
    volumes:
      - ./web:/usr/share/nginx/html
  mc:
    depends_on:
      - web
    image: ${IMAGE_TO_TEST:-itzg/minecraft-server}
    environment:
      EULA: "true"
      SETUP_ONLY: "true"
      ICON: http://web/4737386_minecraft_squircle_icon.png
      # the following are only used to speed up test execution
      TYPE: CUSTOM
      CUSTOM_SERVER: /servers/fake.jar
      VERSION: 1.18.1
    volumes:
      - ./data:/data
      - ./fake.jar:/servers/fake.jar
