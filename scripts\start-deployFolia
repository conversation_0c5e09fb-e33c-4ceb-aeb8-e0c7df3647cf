#!/bin/bash

: "${FOLIA_CHANNEL:=experimental}"

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"
set -o pipefail
handleDebugMode

if [[ $FOLIA_DOWNLOAD_URL ]]; then
  export PAPER_DOWNLOAD_URL="$FOLIA_DOWNLOAD_URL"
fi

if [[ $FOLIABUILD ]]; then
  export PAPERBUILD="$FOLIABUILD"
fi

PAPER_PROJECT="folia" \
PAPER_NAME="FoliaMC" \
PAPER_CHANNEL="${FOLIA_CHANNEL}" \
  exec "${SCRIPTS:-/}start-deployPaper" "$@"
