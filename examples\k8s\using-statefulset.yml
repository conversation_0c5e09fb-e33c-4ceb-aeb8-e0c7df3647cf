---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: mc-example
  name: mc-example
spec:
  replicas: 1
  serviceName: mc-example
  selector:
    matchLabels:
      app: mc-example
  template:
    metadata:
      labels:
        app: mc-example
    spec:
      containers:
        - name: mc
          image: itzg/minecraft-server
          imagePullPolicy: Always
          env:
            - name: EULA
              value: "TRUE"
          volumeMounts:
            - mountPath: /data
              name: data
          readinessProbe:
            exec:
              command:
                - mc-monitor 
                - status 
                - --host 
                - localhost 
                - --port
                - "25565"
            initialDelaySeconds: 30
            periodSeconds: 5
            failureThreshold: 18
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  labels:
    service: mc-example
  name: mc-example
spec:
  ports:
    - port: 25565
      targetPort: 25565
  selector:
    app: mc-example
  type: NodePort
