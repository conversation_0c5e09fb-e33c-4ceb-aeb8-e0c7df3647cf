#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"

: "${RCON_CMDS_STARTUP:=}"
: "${RCON_CMDS_ON_CONNECT:=}"
: "${RCON_CMDS_ON_DISCONNECT:=}"
: "${RCON_CMDS_FIRST_CONNECT:=}"
: "${RCON_CMDS_LAST_DISCONNECT:=}"
: "${RCON_CMDS_PERIOD:=10}"
: "${SERVER_PORT:=25565}"
export RCON_CMDS_STARTUP
export RCON_CMDS_ON_CONNECT
export RCON_CMDS_ON_DISCONNECT
export RCON_CMDS_FIRST_CONNECT
export RCON_CMDS_LAST_DISCONNECT
export RCON_CMDS_PERIOD
export SERVER_PORT

log "Rcon cmds functionality enabled"

isDebugging && set -x

isNumericElseSetToDefault RCON_CMDS_PERIOD 10
checkIfNotZeroElseSetToDefault RCON_CMDS_PERIOD 10

/usr/local/bin/rcon-cmds-daemon.sh &
