#!/bin/bash

# shellcheck source=start-utils
. "${SCRIPTS:-/}start-utils"
isDebugging && set -x

if ! SERVER=$(mc-image-helper github download-latest-asset \
  --output-directory=/data \
  --name-pattern="kettinglauncher-.+?(?<!-sources)\.jar" \
  kettingpowered/kettinglauncher
  ); then
    logError "Failed to download Ketting launcher"
    exit 1
fi

export SERVER

resolveVersion

EXTRA_ARGS+=" -minecraftVersion $VERSION"
if [[ ${KETTING_VERSION:-} ]]; then
  EXTRA_ARGS+=" -kettingVersion $KETTING_VERSION"
fi
if [[ ${FORGE_VERSION:-} ]]; then
  EXTRA_ARGS+=" -forgeVersion $FORGE_VERSION"
fi
export EXTRA_ARGS

export SERVER
export FAMILY=HYBRID
export HYBRIDTYPE=forge

exec "${SCRIPTS:-/}start-spiget" "$@"
