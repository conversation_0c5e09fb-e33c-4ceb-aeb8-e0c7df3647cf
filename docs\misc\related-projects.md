### [itzg/minecraft-bedrock-server](https://github.com/itzg/docker-minecraft-bedrock-server)

Docker image that runs a Minecraft Bedrock server.

### [mc-router](https://github.com/itzg/mc-router)

Lightweight multiplexer/proxy for Minecraft Java servers. Provided as a stand-alone application and a Docker image.

### [itzg/mc-proxy](https://github.com/itzg/docker-bungeecord/)

Docker image that runs a proxy powered by Bungeecord, Velocity, or Waterfall

### [itzg/mc-backup](https://github.com/itzg/docker-mc-backup)

Docker image that runs as a side-car container to backup world data.

### [rcon-cli](https://github.com/itzg/rcon-cli)

A tool that is bundled with this image to provide CLI access to an RCON endpoint.

### [mc-monitor](https://github.com/itzg/mc-monitor)

A tool that is bundled with this image that provides health checks and metrics reporting, such as a Prometheus exporter or a telegraf data source.

### [mc-image-helper](https://github.com/itzg/mc-image-helper)

A tool that is bundled with this image to provide complex, re-usable preparation operations. 

### [itzg/rcon](https://github.com/itzg/docker-rcon-web-admin)

An image that dockerizes [rcon-web-admin](https://github.com/rcon-web-admin/rcon-web-admin).
