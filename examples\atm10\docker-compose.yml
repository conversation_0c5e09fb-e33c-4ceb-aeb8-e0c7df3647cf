services:
  mc:
    image: itzg/minecraft-server
    tty: true
    stdin_open: true
    ports:
      - "25565:25565"
    environment:
      EULA: true
      MOD_PLATFORM: AUTO_CURSEFORGE
      # Allocate API key from https://console.curseforge.com/
      # and set in .env file making sure to double up dollar signs, such as
      # CF_API_KEY=$$2a$$10$$....
      # Refer to https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#api-key
      CF_API_KEY: ${CF_API_KEY}
      CF_SLUG: all-the-mods-10
      # Optional: select a specific version/file
      # CF_FILENAME_MATCHER: "1.17"
      MEMORY: 4G
      CF_OVERRIDES_EXCLUSIONS: |
        shaderpacks/**
    volumes:
      # Use managed volume by default, but can change to a relative path like
      # ./data:/data
      # to use a host directory
      - mc-data:/data
volumes:
  mc-data:
