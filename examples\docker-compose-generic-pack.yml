services:
  mc:
    image: itzg/minecraft-server:${IMAGE_TAG:-latest}
    volumes:
    - data:/data
    - ./modpacks:/modpacks:ro
    environment:
      EULA: "true"
      TYPE: FORGE
      DEBUG: "${DEBUG:-false}"
      VERSION: ${VERSION:-1.17.1}
      FORGE_VERSION: ${FORGE_VERSION:-37.0.90}
      GENERIC_PACK: /modpacks/${MODPACK:-Server-Files-0.0.21.zip}
      REMOVE_OLD_MODS: "${REMOVE_OLD_MODS:-false}"
    ports:
      - "25565:25565"

volumes:
  data: {}